package models

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

// 心愿单
type Wish struct {
	Model

	UserId       uint `gorm:"index"`
	Title        string
	Cover        string
	From         string                  // 出发地
	To           string                  // 目的地
	ToZoneId     uint                    // 目的地城市
	ToPoi        string                  //lng,lat
	BudgetType   constmap.WishBudgetType // 预算类型
	Budget       string                  // 预算
	TotalPeople  int                     // 总人数
	DepartDate   string                  // 出发时间(2025-01-01/2025-01)
	ReturnDate   string                  //结束时间
	Deadline     time.Time               `gorm:"index"` // 截止时间
	OpenScope    int                     //是否公开 1是 2否
	State        constmap.WishState
	RejectReason string
	MemberDesc   string                   `gorm:"size:1024"` // 成员描述
	WishDesc     string                   `gorm:"size:1024"` // 心愿描述
	TagIds       utils.Marshaller[[]uint] `gorm:"size:1024"`
	PlanId       uint

	Todos   []WishTodo
	Members []WishMember
	Medias  []WishMedia
	Ext     WishExt
}

type WishExt struct {
	Model

	WishId    uint   `gorm:"index"`
	RiskCheck string `gorm:"type:text"`
}

// 必做的事
type WishTodo struct {
	Model

	WishId uint `gorm:"index"`
	Todo   string
	IsMust int //是否必做
}

// 心愿标签
type WishTag struct {
	Model

	Tag       string `gorm:"unique;index:idx_tag"`
	WishCount int    `gorm:"index:idx_tag;default:0"`
}

// 心愿成员
type WishMember struct {
	Model

	WishId      uint `gorm:"index"`
	UserId      uint `gorm:"index"`
	RealName    string
	Phone       string
	IsOwner     int
	Budget      string
	Remark      string
	State       constmap.WishMemberState
	FollowState constmap.WishFollowState
}

// 心愿关联媒体资源
type WishMedia struct {
	Model

	WishId    uint `gorm:"index"`
	MediaType constmap.WishMediaType
	Size      int64 //媒体资源大小(字节)
	ResUrl    string
}
